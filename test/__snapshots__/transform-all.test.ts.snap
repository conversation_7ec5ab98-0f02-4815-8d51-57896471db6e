// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`transform-all 1`] = `
"
# Page 

Default Slot


<CodeBlockWrapper v-bind="{lines:true}" :title='""' :ranges='["2","3","4"]'>

\`\`\`ts
function _foo() {
  // ...
}
\`\`\`

</CodeBlockWrapper>


<template v-slot:right="slotProps">


Foo \`{{ code }}\`



</template>
<template v-slot:left="slotProps">

<div>Left Slot</div>


<CodeBlockWrapper v-bind="{}" :title='""' :ranges='[]'>

\`\`\`md
<style>
.text-red { color: red; }
</style>
\`\`\`

</CodeBlockWrapper>


<CodeBlockWrapper v-bind="{}" :title='"with-title"' :ranges='[]'>

\`\`\`js [with-title]
const a = 1
\`\`\`

</CodeBlockWrapper>

::code-group


<CodeBlockWrapper v-bind="{}" :title='"npm"' :ranges='[]'>

\`\`\`sh [npm]
npm i slidev
\`\`\`

</CodeBlockWrapper>


<CodeBlockWrapper v-bind="{}" :title='"yarn"' :ranges='[]'>

\`\`\`sh [yarn]
yarn add slidev
\`\`\`

</CodeBlockWrapper>


<CodeBlockWrapper v-bind="{}" :title='"pnpm"' :ranges='[]'>

\`\`\`sh [pnpm]
pnpm add slidev
\`\`\`

</CodeBlockWrapper>
::

<style scoped>

.text-green { color: green; }
</style>


<CodeBlockWrapper v-bind="{}" :title='""' :ranges='[]'>

\`\`\`ts
function _foo() {
  // ...
}
\`\`\`

</CodeBlockWrapper>


</template>"
`;
