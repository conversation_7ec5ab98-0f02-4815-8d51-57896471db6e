// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`external snippet 1`] = `
"
\`\`\`ts {2|3|4}{lines:true}
function _foo() {
  // ...
}
\`\`\`
"
`;

exports[`inline CSS 1`] = `
"
# Page 

<style scoped>

h1 {
  color: red;
}
</style>


<CodeBlockWrapper v-bind="{}" :title='""' :ranges='[]'>

\`\`\`css
<style>
h1 {
  color: green;
}
</style>
\`\`\`

</CodeBlockWrapper>
"
`;

exports[`mermaid 1`] = `
"
# Page 

<Mermaid code-lz="M4UwjgriB2DGIBECWBDA5gJxQWwFAAJ8BBAGyXgFoA+AKQHsALaALnwAkQSS796mAafAzoB3fCgwh8ATzoQA/AXwA5OgBcpdAG4gMxMvH58WxfGukAHcihL4k0DVlhqkdaEA" v-bind="{}" />

<Mermaid code-lz="OYJwhgDgFgBAKgEQFACEDacCmAPALgXRgFoiA+GAYQG8FMBjASwGcGB7AOwF8kLiyAfAPLtM/GAjQAlTEwCuAG1wwAjPh59S/OAHdWYgKJSZCpQCZ8QA" v-bind="{theme: 'neutral', scale: 0.8}" />
"
`;

exports[`slot-sugar 1`] = `
"
# Page 

Default Slot

<template v-slot:right="slotProps">

Right Slot


</template>
<template v-slot:left="slotProps">

<div>Left Slot</div>


</template>"
`;

exports[`slot-sugar with code 1`] = `
"
# Page 

Default Slot


<template v-slot:code="slotProps">



<CodeBlockWrapper v-bind="{}" :title='""' :ranges='[]'>

\`\`\`md
Slot Usage
::right::
::left::
\`\`\`

</CodeBlockWrapper>



</template>"
`;

exports[`slot-sugar with default 1`] = `
"

<template v-slot:right="slotProps">

Right Slot


</template>
<template v-slot:left="slotProps">

<div>Left Slot</div>


</template>
<template v-slot:default="slotProps">

# Page 
Default Slot


</template>"
`;

exports[`slot-sugar with symbols in name 1`] = `
"
# Page 

Default Slot

<template v-slot:slot::1="slotProps">

First Slot


</template>
<template v-slot:slot.2="slotProps">

Second Slot


</template>"
`;
