<!-- filepath: /Users/<USER>/xhs_eng/course/slidev/demo/starter/components/HandwritingCanvas.vue -->
<template>
  <div
    v-show="isActive"
    class="handwriting-container"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
  >
    <div class="toolbar">
      <button @click="toggleHandwriting" class="control-btn">关闭手写</button>
      <button @click="clearCanvas" class="control-btn">清除</button>
    </div>
    <canvas ref="canvas"></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useNav } from '@slidev/client'

const canvas = ref(null)
const ctx = ref(null)
const isActive = ref(false)
const { next, prev } = useNav()
const startX = ref(0)
const startY = ref(0)
let isDrawing = false

onMounted(() => {
  ctx.value = canvas.value.getContext('2d')
  canvas.value.width = window.innerWidth
  canvas.value.height = window.innerHeight

  // 注册快捷键 Cmd+P 或 Ctrl+P 开关手写模式
  window.addEventListener('keydown', (e) => {
    if (e.key === 'p' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      toggleHandwriting()
    }
  })
})

function toggleHandwriting() {
  isActive.value = !isActive.value
}

function clearCanvas() {
  if (ctx.value) {
    ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height)
  }
}

function handleTouchStart(event) {
  const touch = event.touches[0]
  startX.value = touch.clientX
  startY.value = touch.clientY

  if (touch.touchType === 'pencil' || (touch.force && touch.radiusX)) {
    isDrawing = true
    ctx.value.beginPath()
    ctx.value.moveTo(touch.clientX, touch.clientY)
    ctx.value.lineWidth = 2
    ctx.value.strokeStyle = '#000'
    event.preventDefault()
  }
}

function handleTouchMove(event) {
  const touch = event.touches[0]
  if (touch.touchType === 'pencil' || (touch.force && touch.radiusX)) {
    if (!isDrawing) return
    ctx.value.lineTo(touch.clientX, touch.clientY)
    ctx.value.stroke()
    event.preventDefault()
    return
  }

  const deltaX = touch.clientX - startX.value
  if (Math.abs(deltaX) > 50) {
    if (deltaX > 0) {
      prev()
    } else {
      next()
    }
    startX.value = touch.clientX
  }
}

function handleTouchEnd() {
  isDrawing = false
}
</script>

<style scoped>
.handwriting-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.8);
  touch-action: none;
}

.toolbar {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 10000;
}

.control-btn {
  padding: 8px 16px;
  background: #4a5568;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

canvas {
  width: 100%;
  height: 100%;
}
</style>