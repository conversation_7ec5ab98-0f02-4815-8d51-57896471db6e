{"private": true, "scripts": {"build": "slidev build", "dev": "nodemon -w '../../packages/slidev/dist/*.mjs' --exec \"slidev ./slides.md --open=false --log=info --inspect\"", "export": "slidev export", "export-notes": "slidev export-notes"}, "devDependencies": {"@slidev/cli": "workspace:*", "@slidev/theme-default": "catalog:themes", "@slidev/theme-seriph": "catalog:themes", "@vue/compiler-sfc": "catalog:demo", "nodemon": "catalog:dev", "vue": "catalog:frontend"}}