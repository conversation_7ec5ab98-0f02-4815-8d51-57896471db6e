<!--
PlantUML
(auto transformed, you don't need to use this component directly)

Usage:

```plantuml
@startuml
Alice -> Bob : Hello!
@enduml
```
-->
<script setup lang="ts">
import { computed } from 'vue'

const props = withDefaults(defineProps<{
  code: string
  server: string
  scale?: number
  alt?: string
}>(), {
  alt: 'PlantUML diagram',
})

const uri = computed(() => `${props.server}/svg/${props.code}`)
</script>

<template>
  <img :src="uri" :style="{ scale }" :alt="alt">
</template>
