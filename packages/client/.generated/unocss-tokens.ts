/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
export default [
  "!backdrop-blur-0px",
  "!bg-opacity-75",
  "!bg-transparent",
  "!border-none",
  "!hidden",
  "!opacity-0",
  "!opacity-100",
  "!p-4",
  "!px-0",
  "!text-current",
  "!text-sm",
  "-mt-0.5",
  "-mt-1",
  "-rotate-45",
  "-top-15px",
  "-top-20",
  "-translate-y-1/2",
  "-z-1",
  "<md:hidden",
  "[absolute=\"\"]",
  "[align-top=\"\"]",
  "[b=\"\"]",
  "[bg-cyan:10=\"\"]",
  "[bg-gray:4=\"\"]",
  "[bg-gray:5=\"\"]",
  "[bg-gray=\"\"]",
  "[bg-opacity-30=\"\"]",
  "[bg-primary=\"\"]",
  "[bg~=\"black\"]",
  "[bg~=\"opacity-80\"]",
  "[border-collapse=\"\"]",
  "[border-gray=\"\"]",
  "[border-main=\"\"]",
  "[border-t=\"\"]",
  "[border=\"\"]",
  "[border~=\"0\"]",
  "[border~=\"1\"]",
  "[border~=\"b\"]",
  "[border~=\"dark:main\"]",
  "[border~=\"main\"]",
  "[border~=\"r\"]",
  "[border~=\"red\"]",
  "[border~=\"rounded\"]",
  "[border~=\"rounded-lg\"]",
  "[border~=\"rounded-md\"]",
  "[border~=\"t\"]",
  "[border~=\"transparent\"]",
  "[border~=\"y\"]",
  "[border~=\"~\"]",
  "[bottom-0=\"\"]",
  "[bottom-1=\"\"]",
  "[bottom-5=\"\"]",
  "[b~=\"100%\"]",
  "[b~=\"2\"]",
  "[b~=\"50%\"]",
  "[b~=\"x\"]",
  "[b~=\"y\"]",
  "[color~=\"green\"]",
  "[container=\"\"]",
  "[cursor-pointer=\"\"]",
  "[dark:bg-gray-800=\"\"]",
  "[dark:border-gray-500=\"\"]",
  "[dark:border~=\"main\"]",
  "[dark:border~=\"~\"]",
  "[fixed=\"\"]",
  "[flex-auto=\"\"]",
  "[flex=\"\"]",
  "[flex~=\"col\"]",
  "[flex~=\"gap-1\"]",
  "[flex~=\"gap-2\"]",
  "[flex~=\"gap-3\"]",
  "[flex~=\"gap-4\"]",
  "[flex~=\"items-center\"]",
  "[flex~=\"justify-center\"]",
  "[flex~=\"row\"]",
  "[flex~=\"~\"]",
  "[font-bold=\"\"]",
  "[font-mono=\"\"]",
  "[grid=\"\"]",
  "[h-1.5=\"\"]",
  "[h-1.5em=\"\"]",
  "[h-1px=\"\"]",
  "[h-22px=\"\"]",
  "[h-2=\"\"]",
  "[h-5=\"\"]",
  "[h-8=\"\"]",
  "[h-full=\"\"]",
  "[h1=\"\"]",
  "[h5=\"\"]",
  "[hidden=\"\"]",
  "[hover:bg-active=\"\"]",
  "[hover~=\"op100\"]",
  "[inline-flex=\"\"]",
  "[inset-0=\"\"]",
  "[italic=\"\"]",
  "[items-center=\"\"]",
  "[justify-center=\"\"]",
  "[left-0=\"\"]",
  "[m--1=\"\"]",
  "[m0=\"\"]",
  "[marker-start~=\"none\"]",
  "[max-w-90=\"\"]",
  "[min-w-30=\"\"]",
  "[min-w-90=\"\"]",
  "[my2=\"\"]",
  "[of-hidden=\"\"]",
  "[op0=\"\"]",
  "[op25=\"\"]",
  "[op40=\"\"]",
  "[op50=\"\"]",
  "[op75=\"\"]",
  "[opacity-10=\"\"]",
  "[p0.5=\"\"]",
  "[p1=\"\"]",
  "[p2=\"\"]",
  "[p3=\"\"]",
  "[pl-4=\"\"]",
  "[pl1=\"\"]",
  "[pl2=\"\"]",
  "[pointer-events-none=\"\"]",
  "[pr-3=\"\"]",
  "[pr-4=\"\"]",
  "[print:hidden=\"\"]",
  "[pt-2px=\"\"]",
  "[px2=\"\"]",
  "[px3=\"\"]",
  "[px4=\"\"]",
  "[px=\"\"]",
  "[py-2=\"\"]",
  "[py0=\"\"]",
  "[py2=\"\"]",
  "[p~=\"l-1\"]",
  "[p~=\"r-2\"]",
  "[p~=\"t-0.5\"]",
  "[p~=\"x-4\"]",
  "[p~=\"y-2\"]",
  "[relative=\"\"]",
  "[resize=\"\"]",
  "[right--2=\"\"]",
  "[right-0.5=\"\"]",
  "[right-0=\"\"]",
  "[right-1=\"\"]",
  "[right-5=\"\"]",
  "[rounded-full=\"\"]",
  "[rounded=\"\"]",
  "[scale~=\"0.5\"]",
  "[scale~=\"1\"]",
  "[select-none=\"\"]",
  "[shadow=\"\"]",
  "[shadow~=\"$event\"]",
  "[shadow~=\"~\"]",
  "[slidev-glass-effect=\"\"]",
  "[start~=\"1\"]",
  "[stroke-width~=\"1\"]",
  "[stroke-width~=\"2\"]",
  "[stroke-width~=\"20\"]",
  "[stroke-width~=\"3\"]",
  "[stroke~=\"transparent\"]",
  "[table-cell=\"\"]",
  "[table-row=\"\"]",
  "[table=\"\"]",
  "[text-cyan:75=\"\"]",
  "[text-main=\"\"]",
  "[text-primary=\"\"]",
  "[text-right=\"\"]",
  "[text-sm=\"\"]",
  "[text-xs=\"\"]",
  "[text~=\"sm\"]",
  "[top-0.5=\"\"]",
  "[top-0=\"\"]",
  "[vertical-middle=\"\"]",
  "[visible=\"\"]",
  "[w-1.5=\"\"]",
  "[w-1px=\"\"]",
  "[w-20=\"\"]",
  "[w-2=\"\"]",
  "[w-30=\"\"]",
  "[w-5=\"\"]",
  "[w-60=\"\"]",
  "[w-90=\"\"]",
  "[w-full=\"\"]",
  "[w1=\"\"]",
  "[ws-nowrap=\"\"]",
  "[z-10=\"\"]",
  "[z-1=\"\"]",
  "[z-label=\"\"]",
  "absolute",
  "align-middle",
  "align-top",
  "animate-duration-100",
  "animate-fade-in",
  "animate-pulse",
  "aspect-initial",
  "aspect-ratio-initial",
  "auto-rows-fr",
  "b",
  "b-dark",
  "b-dashed",
  "backdrop-blur-5px",
  "bg-active",
  "bg-black",
  "bg-blue-400",
  "bg-current",
  "bg-cyan:10",
  "bg-gray",
  "bg-gray-400",
  "bg-gray/10",
  "bg-gray/20",
  "bg-gray/5",
  "bg-gray:10",
  "bg-gray:4",
  "bg-gray:5",
  "bg-main",
  "bg-opacity-10",
  "bg-opacity-30",
  "bg-opacity-50",
  "bg-orange/10",
  "bg-primary",
  "bg-transparent",
  "block",
  "border",
  "border-2",
  "border-b",
  "border-b-2",
  "border-blue-600",
  "border-current",
  "border-dashed",
  "border-gray",
  "border-gray-300/50",
  "border-gray-400",
  "border-l",
  "border-main",
  "border-opacity-20",
  "border-orange",
  "border-primary",
  "border-r",
  "border-r-2",
  "border-t",
  "border-transparent",
  "border-white",
  "bottom-0",
  "bottom-1",
  "bottom-10",
  "bottom-5",
  "break-after-page",
  "break-all",
  "break-inside-avoid-page",
  "caret-black",
  "children:my-auto",
  "container",
  "cursor-default",
  "cursor-pointer",
  "cursor-text",
  "dark:b-gray-400",
  "dark:bg-gray-800",
  "dark:border",
  "dark:border-gray-500",
  "dark:border-true-gray-700",
  "dark:caret-white",
  "dark:stroke-black",
  "dark:text-gray-200",
  "dark:text-green",
  "dark:text-red-500",
  "duration-150",
  "duration-200",
  "duration-300",
  "duration-400",
  "duration-500",
  "ease-in",
  "ease-out",
  "filter",
  "fixed",
  "flex",
  "flex-1",
  "flex-auto",
  "flex-col",
  "flex-grow",
  "flex-none",
  "flex-nowrap",
  "flex-wrap",
  "flex-wrap-reverse",
  "focus-visible:opacity-100",
  "focus-within:opacity-100",
  "focus:outline-none",
  "font-$slidev-code-font-family",
  "font-500",
  "font-bold",
  "font-light",
  "font-mono",
  "gap-0.2",
  "gap-0.5",
  "gap-1",
  "gap-2",
  "gap-3",
  "gap-4",
  "gap-5",
  "gap-x-8",
  "gap-y-4",
  "grid",
  "grid-cols-2",
  "grid-cols-[1fr_max-content]",
  "grid-cols-[35px_1fr]",
  "grid-rows-[1fr_max-content]",
  "grid-rows-[1fr_min-content]",
  "grid-rows-[auto_max-content]",
  "grid-rows-[max-content_1fr]",
  "group-hover:hidden",
  "group-hover:op100",
  "group-hover:op80",
  "group-hover:opacity-20",
  "group-not-hover:hidden",
  "h-0.7",
  "h-1.5",
  "h-10px",
  "h-1px",
  "h-2",
  "h-22px",
  "h-3.5",
  "h-30",
  "h-3px",
  "h-40",
  "h-40px",
  "h-5",
  "h-6",
  "h-8",
  "h-9",
  "h-[40px]",
  "h-[calc(var(--vh,1vh)*100)]",
  "h-full",
  "h-max",
  "h-screen",
  "h1",
  "h2",
  "h5",
  "hidden",
  "hover:!opacity-100",
  "hover:bg-active",
  "hover:bg-gray-400",
  "hover:bg-gray/20",
  "hover:bg-opacity-10",
  "hover:border-primary",
  "hover:border-solid",
  "hover:op-100",
  "hover:op100",
  "hover:opacity-10",
  "hover:opacity-100",
  "hover:opacity-90",
  "hover:text-primary",
  "important-text-op-50",
  "important:[&_*]:select-none",
  "important:op0",
  "inline",
  "inline-block",
  "inline-flex",
  "inset-0",
  "inset-0.5",
  "inset-y-2",
  "italic",
  "items-center",
  "items-end",
  "justify-center",
  "justify-items-start",
  "leading-1.6em",
  "leading-1em",
  "leading-2",
  "leading-5",
  "leading-[.8rem]",
  "left-0",
  "left-1",
  "left-1/2",
  "left-110%",
  "left-3",
  "lg:m-2",
  "lg:p-2",
  "lg:p-4",
  "list-disc",
  "lt-md:flex-col",
  "lt-md:hidden",
  "m-1",
  "m-4",
  "m-auto",
  "m0",
  "ma",
  "max-h-full",
  "max-w-100",
  "max-w-150",
  "max-w-250",
  "max-w-300",
  "max-w-90",
  "max-w-full",
  "max-w-xs",
  "mb--.5",
  "mb-10",
  "mb-2",
  "mb-4",
  "mb-8",
  "mb2",
  "md:flex-col",
  "md:flex-nowrap",
  "md:flex-row",
  "md:gap-8",
  "md:my-4",
  "md:of-y-auto",
  "min-h-50",
  "min-h-full",
  "min-w-16",
  "min-w-30",
  "min-w-40",
  "min-w-90",
  "min-w-fit",
  "min-w-full",
  "min-w-max",
  "ml--4",
  "ml-1",
  "mr--3",
  "mr--8",
  "mr-1",
  "mr-2",
  "mr-4",
  "mr1",
  "mt-0.5",
  "mt-1",
  "mt-2",
  "mt-3",
  "mt1",
  "mx--1.2",
  "mx-auto",
  "my-1",
  "my-10px",
  "my-4",
  "my-auto",
  "my1",
  "my2",
  "my4",
  "my5",
  "mya",
  "object-contain",
  "object-cover",
  "of-auto",
  "of-hidden",
  "of-x-visible",
  "of-y-auto",
  "op-40",
  "op-60",
  "op-70",
  "op-80",
  "op0",
  "op100",
  "op15",
  "op20",
  "op25",
  "op30",
  "op35",
  "op40",
  "op50",
  "op75",
  "op80",
  "opacity-0",
  "opacity-10",
  "opacity-25",
  "opacity-40",
  "opacity-5",
  "opacity-50",
  "opacity-80",
  "origin-tl",
  "outline",
  "outline-none",
  "overflow-auto",
  "overflow-hidden",
  "overflow-visible",
  "overflow-x-hidden",
  "overflow-y-auto",
  "overflow-y-hidden",
  "p-1",
  "p-16",
  "p-2",
  "p-6",
  "p0.5",
  "p1",
  "p2",
  "p3",
  "p4",
  "pa-3",
  "pb-2",
  "pb2",
  "pie",
  "pl-0",
  "pl-4",
  "pl1",
  "pl2",
  "place-content-center",
  "pointer-events-none",
  "pr-12",
  "pr-3",
  "pr-4",
  "print-container",
  "print:block",
  "print:hidden",
  "print:inset-0",
  "print:min-h-max",
  "print:position-unset",
  "prose",
  "pt-.5",
  "pt-1",
  "pt-15%",
  "pt-2",
  "pt-4",
  "pt5",
  "px",
  "px-1.5",
  "px-2",
  "px-3",
  "px-4",
  "px-5",
  "px-6",
  "px1",
  "px2",
  "px3",
  "px4",
  "py-1",
  "py-10",
  "py-2",
  "py-20",
  "py-3",
  "py-4",
  "py0",
  "py0.5",
  "py1",
  "py1.5",
  "py2",
  "py3",
  "relative",
  "resize",
  "resize-none",
  "right--2",
  "right-0",
  "right-0.5",
  "right-1",
  "right-3",
  "right-4",
  "right-5",
  "rounded",
  "rounded-1/2",
  "rounded-b",
  "rounded-full",
  "rounded-l",
  "rounded-lb",
  "rounded-lg",
  "rounded-md",
  "rounded-r",
  "rounded-tl",
  "scale-102",
  "scale-85",
  "scale-x-80",
  "select-none",
  "select-text",
  "shadow",
  "shadow-xl",
  "slidev-glass-effect",
  "sr-only",
  "stroke-white",
  "tab",
  "table",
  "table-cell",
  "table-row",
  "tabular-nums",
  "text-$slidev-controls-foreground",
  "text-1.2em",
  "text-11px",
  "text-2xl",
  "text-3em",
  "text-3xl",
  "text-4xl",
  "text-9xl",
  "text-base",
  "text-blue-500",
  "text-center",
  "text-current",
  "text-cyan:75",
  "text-gray-400",
  "text-gray-500",
  "text-green",
  "text-green-500",
  "text-green6",
  "text-lg",
  "text-main",
  "text-nowrap",
  "text-opacity-50!",
  "text-opacity-85",
  "text-orange-500",
  "text-orange/100",
  "text-primary",
  "text-red-400",
  "text-red-500",
  "text-red-700",
  "text-right",
  "text-sm",
  "text-teal-700",
  "text-transparent",
  "text-white",
  "text-wrap",
  "text-xl",
  "text-xs",
  "top-0",
  "top-0.5",
  "top-1",
  "top-1/2",
  "top-10",
  "top-4",
  "top-5",
  "top-50%",
  "touch-none",
  "tracking-widest",
  "transform",
  "transition",
  "transition-all",
  "transition-opacity",
  "translate-0",
  "translate-y--50%",
  "underline",
  "uppercase",
  "view-transition-name",
  "visible",
  "w-0",
  "w-1.5",
  "w-10px",
  "w-13",
  "w-1px",
  "w-2",
  "w-20",
  "w-200",
  "w-22px",
  "w-250",
  "w-3.5",
  "w-30",
  "w-5",
  "w-6",
  "w-60",
  "w-7",
  "w-8",
  "w-9",
  "w-90",
  "w-[40px]",
  "w-full",
  "w-screen",
  "w1",
  "whitespace-nowrap",
  "ws-nowrap",
  "z-1",
  "z-10",
  "z-camera",
  "z-context-menu",
  "z-dragging",
  "z-label",
  "z-menu",
  "z-modal",
  "z-nav"
]