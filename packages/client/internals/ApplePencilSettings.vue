<script setup lang="ts">
import { useDrawings } from '../composables/useDrawings'
import FormCheckbox from './FormCheckbox.vue'
import FormItem from './FormItem.vue'
import SegmentControl from './SegmentControl.vue'

const { applePencilDoubleTapAction, pressureSensitive, isApplePencilSupported } = useDrawings()

const doubleTapOptions = [
  { label: '橡皮擦', value: 'eraser' },
  { label: '撤销', value: 'undo' },
  { label: '换色', value: 'colorSwitch' },
  { label: '大小', value: 'sizeToggle' },
]
</script>

<template>
  <div text-sm select-none flex="~ col gap-1" min-w-30 px4>
    <div class="text-xs opacity-75 mb-2">
      Apple Pencil 设置
    </div>

    <div v-if="!isApplePencilSupported" class="text-xs opacity-60 p-2 bg-yellow-500/10 rounded">
      ⚠️ 当前设备可能不支持 Apple Pencil 功能
    </div>

    <FormItem
      title="双击操作"
      description="设置 Apple Pencil 双击时执行的操作"
    >
      <SegmentControl
        v-model="applePencilDoubleTapAction"
        :options="doubleTapOptions"
      />
    </FormItem>

    <FormItem
      title="压感检测"
      description="根据 Apple Pencil 压感调整画笔大小"
    >
      <FormCheckbox v-model="pressureSensitive" />
    </FormItem>

    <div class="text-xs opacity-60 mt-2">
      <div class="mb-1">• 橡皮擦：切换橡皮擦/画笔模式</div>
      <div class="mb-1">• 撤销：撤销上一步操作</div>
      <div class="mb-1">• 换色：循环切换画笔颜色</div>
      <div class="mb-1">• 大小：循环切换画笔大小</div>
      <div class="mb-1">• 压感：根据按压力度调整粗细</div>
    </div>
  </div>
</template>
