<script setup lang="ts">
import { useDrawings } from '../composables/useDrawings'
import FormCheckbox from './FormCheckbox.vue'
import FormItem from './FormItem.vue'
import SegmentControl from './SegmentControl.vue'

const { applePencilLongPressAction, pressureSensitive, isApplePencilSupported } = useDrawings()

const longPressOptions = [
  { label: '橡皮擦', value: 'eraser' },
  { label: '撤销', value: 'undo' },
  { label: '换色', value: 'colorSwitch' },
  { label: '大小', value: 'sizeToggle' },
]
</script>

<template>
  <div text-sm select-none flex="~ col gap-1" min-w-30 px4>
    <div class="text-xs opacity-75 mb-2">
      Apple Pencil 设置
    </div>

    <div class="text-xs opacity-60 p-2 bg-blue-500/10 rounded mb-2">
      ℹ️ 注意：Apple Pencil 的硬件双击功能在 Web 浏览器中不可用，这是系统限制
    </div>

    <div v-if="!isApplePencilSupported" class="text-xs opacity-60 p-2 bg-yellow-500/10 rounded">
      ⚠️ 当前设备可能不支持 Apple Pencil 功能
    </div>

    <FormItem
      title="长按操作"
      description="设置 Apple Pencil 长按时执行的操作（替代双击）"
    >
      <SegmentControl
        v-model="applePencilLongPressAction"
        :options="longPressOptions"
      />
    </FormItem>

    <FormItem
      title="压感检测"
      description="根据 Apple Pencil 压感调整画笔大小"
    >
      <FormCheckbox v-model="pressureSensitive" />
    </FormItem>

    <div class="text-xs opacity-60 mt-2">
      <div class="mb-1">• 橡皮擦：切换橡皮擦/画笔模式</div>
      <div class="mb-1">• 撤销：撤销上一步操作</div>
      <div class="mb-1">• 换色：循环切换画笔颜色</div>
      <div class="mb-1">• 大小：循环切换画笔大小</div>
      <div class="mb-1">• 压感：根据按压力度调整粗细（支持）</div>
      <div class="mb-1">• 长按：在画布上长按 Apple Pencil 触发操作</div>
    </div>
  </div>
</template>
