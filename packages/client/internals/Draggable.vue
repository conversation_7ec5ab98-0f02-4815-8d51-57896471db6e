<script setup lang="ts">
import { useDraggable, useLocalStorage } from '@vueuse/core'
import { computed, ref } from 'vue'

const props = defineProps<{
  storageKey?: string
  initial?: { x: number, y: number }
  disabled?: boolean
}>()

const el = ref<HTMLElement | null>(null)
const initial = props.initial ?? { x: 0, y: 0 }
const point = props.storageKey
  ? useLocalStorage(props.storageKey, initial)
  : ref(initial)

// 当disabled为true时，禁用拖动
const { style } = useDraggable(el, {
  initialValue: point,
  disabled: computed(() => props.disabled)
})
</script>

<template>
  <div ref="el" class="fixed" :style="style">
    <slot />
  </div>
</template>
