import type { Brush, Options as DrauuOptions, DrawingMode } from 'drauu'
import { createSharedComposable, toReactive, useLocalStorage } from '@vueuse/core'
import { createDrauu } from 'drauu'
import { computed, markRaw, nextTick, reactive, ref, watch } from 'vue'
import { configs } from '../env'
import { isInputting } from '../state'
import { drawingState, onPatchDrawingState, patchDrawingState } from '../state/drawings'
import { useNav } from './useNav'

export const useDrawings = createSharedComposable(() => {
  const { currentSlideNo, isPresenter } = useNav()

  const brushColors = [
    '#ff595e',
    '#ffca3a',
    '#8ac926',
    '#1982c4',
    '#6a4c93',
    '#ffffff',
    '#000000',
  ]

  const drawingEnabled = useLocalStorage('slidev-drawing-enabled', false)
  const drawingPinned = useLocalStorage('slidev-drawing-pinned', false)
  // 根据设备像素比调整笔刷大小
  const getDefaultBrushSize = () => {
    if (typeof window === 'undefined') return 4
    // iPad 等高分辨率设备需要更大的笔刷
    if (typeof navigator !== 'undefined' && /iPad/i.test(navigator.userAgent)) {
      // iPad 上基础笔刷大小设置更大，因为有压感
      return 26
    }
    const pixelRatio = window.devicePixelRatio || 1
    return Math.max(4, Math.floor(4 * pixelRatio))
  }

  const brush = toReactive(useLocalStorage<Brush>('slidev-drawing-brush', {
    color: brushColors[0],
    size: getDefaultBrushSize(),
    mode: 'stylus',
  }))

  const isDrawing = ref(false)
  const canUndo = ref(false)
  const canRedo = ref(false)
  const canClear = ref(false)

  const _mode = ref<DrawingMode | 'arrow'>('stylus')
  const syncUp = computed(() => configs.drawings.syncAll || isPresenter.value)
  let disableDump = false

  // Apple Pencil 双击检测
  const applePencilDoubleTapAction = useLocalStorage('slidev-apple-pencil-double-tap', 'eraser')
  let lastPencilTapTime = 0
  let lastPencilTapX = 0
  let lastPencilTapY = 0
  let pencilTapCount = 0
  const DOUBLE_TAP_TIME_THRESHOLD = 300 // 毫秒
  const DOUBLE_TAP_DISTANCE_THRESHOLD = 50 // 像素

  const drawingMode = computed({
    get() {
      return _mode.value
    },
    set(v: DrawingMode | 'arrow') {
      _mode.value = v
      if (v === 'arrow') {
        // eslint-disable-next-line ts/no-use-before-define
        drauu.mode = 'line'
        brush.arrowEnd = true
      }
      else {
        // eslint-disable-next-line ts/no-use-before-define
        drauu.mode = v
        brush.arrowEnd = false
      }
    },
  })

  let touchStartX = 0
  let touchStartY = 0

  // 检查是否是工具栏相关元素的通用函数
  function isToolbarElement(target: HTMLElement): boolean {
    return target?.closest('.slidev-toolbar') ||
           target?.closest('.v-popper__wrapper') ||
           target?.closest('.v-popper--theme-menu') ||
           target?.closest('[role="slider"]') ||
           target?.matches('input[type="range"]') ||
           target?.closest('.slidev-brush-size') ||
           target?.closest('[data-floating-ui-portal]') ||
           target?.closest('.floating-vue') ||
           target?.closest('.slidev-icon-btn') ||
           target?.closest('[class*="z-nav"]') ||
           target?.closest('[class*="bg-main"]')
  }

  // 显示双击反馈的函数
  function showDoubleTapFeedback(message: string) {
    // 创建临时提示元素
    const feedback = document.createElement('div')
    feedback.textContent = message
    feedback.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 10000;
      pointer-events: none;
      animation: fadeInOut 1s ease-in-out;
    `

    // 添加CSS动画
    if (!document.getElementById('apple-pencil-feedback-style')) {
      const style = document.createElement('style')
      style.id = 'apple-pencil-feedback-style'
      style.textContent = `
        @keyframes fadeInOut {
          0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
          20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
          80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
          100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        }
      `
      document.head.appendChild(style)
    }

    document.body.appendChild(feedback)

    // 1秒后移除
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback)
      }
    }, 1000)
  }

  // Apple Pencil 双击检测函数
  function handleApplePencilDoubleTap() {
    if (!drawingEnabled.value) return

    let feedbackMessage = ''

    switch (applePencilDoubleTapAction.value) {
      case 'eraser':
        // 切换到橡皮擦模式
        if (drawingMode.value === 'eraseLine') {
          // 如果已经是橡皮擦，切换回上一个绘图模式
          drawingMode.value = 'stylus'
          feedbackMessage = '切换到画笔'
        } else {
          // 切换到橡皮擦
          drawingMode.value = 'eraseLine'
          feedbackMessage = '切换到橡皮擦'
        }
        break
      case 'undo':
        // 执行撤销操作
        if (canUndo.value) {
          drauu.undo()
          feedbackMessage = '撤销'
        } else {
          feedbackMessage = '无法撤销'
        }
        break
      case 'colorSwitch':
        // 切换画笔颜色
        const currentIndex = brushColors.indexOf(brush.color)
        const nextIndex = (currentIndex + 1) % brushColors.length
        brush.color = brushColors[nextIndex]
        feedbackMessage = '切换颜色'
        break
      case 'sizeToggle':
        // 切换画笔大小（小/中/大）
        const sizes = [2, 8, 15]
        const currentSizeIndex = sizes.findIndex(size => Math.abs(size - brush.size) < 2)
        const nextSizeIndex = (currentSizeIndex + 1) % sizes.length
        brush.size = sizes[nextSizeIndex]
        const sizeNames = ['小', '中', '大']
        feedbackMessage = `画笔大小: ${sizeNames[nextSizeIndex]}`
        break
    }

    if (feedbackMessage) {
      showDoubleTapFeedback(feedbackMessage)
    }
  }

  // 检测 Apple Pencil 双击的函数
  function detectApplePencilDoubleTap(e: PointerEvent) {
    // 只处理 Apple Pencil (pen 类型)
    if (e.pointerType !== 'pen') return false

    const now = Date.now()
    const timeDiff = now - lastPencilTapTime
    const distanceX = Math.abs(e.clientX - lastPencilTapX)
    const distanceY = Math.abs(e.clientY - lastPencilTapY)
    const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY)

    // 检测双击：时间间隔和位置都在阈值内
    if (timeDiff < DOUBLE_TAP_TIME_THRESHOLD && distance < DOUBLE_TAP_DISTANCE_THRESHOLD) {
      pencilTapCount++
      if (pencilTapCount === 2) {
        // 检测到双击
        handleApplePencilDoubleTap()
        pencilTapCount = 0
        lastPencilTapTime = 0
        return true
      }
    } else {
      pencilTapCount = 1
    }

    lastPencilTapTime = now
    lastPencilTapX = e.clientX
    lastPencilTapY = e.clientY
    return false
  }

  // Apple Pencil 检测和压感支持
  const isApplePencilSupported = computed(() => {
    // 检测是否为 iPad 设备且支持 pointer events
    return typeof navigator !== 'undefined' &&
           /iPad/i.test(navigator.userAgent) &&
           'PointerEvent' in window
  })

  const pressureSensitive = useLocalStorage('slidev-apple-pencil-pressure', true)
  const baseBrushSize = ref(brush.size)

  // 监听画笔大小变化，更新基础大小
  watch(() => brush.size, (newSize) => {
    if (!isDrawing.value) {
      baseBrushSize.value = newSize
    }
  })

  const drauuOptions: DrauuOptions = reactive({
    brush,
    acceptsInputTypes: computed(() => {
      if (!drawingEnabled.value || (configs.drawings.presenterOnly && !isPresenter.value))
        return ['pen' as const]
      // iPad 设备只接受 pen
      if (typeof navigator !== 'undefined' && /iPad/i.test(navigator.userAgent))
        return ['pen' as const]
      // 其他设备接受所有输入
      return undefined
    }),
    coordinateTransform: false,
    onPointerDown: (e: PointerEvent) => {
      // 检测 Apple Pencil 双击
      if (e.pointerType === 'pen') {
        const isDoubleTap = detectApplePencilDoubleTap(e)
        if (isDoubleTap) {
          aler
          e.preventDefault()
          return false // 阻止绘图，因为这是双击操作
        }

        // Apple Pencil 压感检测
        if (pressureSensitive.value && e.pressure > 0) {
          // 根据压感调整画笔大小 (压感范围通常是 0-1)
          const pressureMultiplier = Math.max(0.3, Math.min(2.0, e.pressure * 2))
          brush.size = Math.round(baseBrushSize.value * pressureMultiplier)
        }
      }

      if (e.pointerType === 'touch') {
        // 如果是工具栏元素，允许事件继续
        if (isToolbarElement(e.target as HTMLElement)) {
          return true
        }

        touchStartX = e.clientX
        touchStartY = e.clientY
        e.preventDefault()
        return false
      }
      return true
    },
    onPointerMove: (e: PointerEvent) => {
      if (e.pointerType === 'touch') {
        // 如果是工具栏元素，允许事件继续
        if (isToolbarElement(e.target as HTMLElement)) {
          return true
        }

        const deltaX = e.clientX - touchStartX
        if (Math.abs(deltaX) > 50) {
          if (deltaX > 0)
            useNav().prev()
          else
            useNav().next()
          touchStartX = e.clientX
        }
        e.preventDefault()
        return false
      }
      return true
    }
  })

  const drauu = markRaw(createDrauu(drauuOptions))

  function clearDrauu() {
    drauu.clear()
    if (syncUp.value)
      patchDrawingState(currentSlideNo.value, '')
  }

  function updateState() {
    canRedo.value = drauu.canRedo()
    canUndo.value = drauu.canUndo()
    canClear.value = !!drauu.el?.children.length
  }

  function loadCanvas(page?: number) {
    disableDump = true
    const data = drawingState[page || currentSlideNo.value]
    if (data != null)
      drauu.load(data)
    else
      drauu.clear()
    updateState()
    disableDump = false
  }

  drauu.on('changed', () => {
    updateState()
    if (!disableDump) {
      const dump = drauu.dump()
      const key = currentSlideNo.value
      if ((drawingState[key] || '') !== dump && syncUp.value)
        patchDrawingState(key, drauu.dump())
    }
  })

  onPatchDrawingState((state) => {
    disableDump = true
    if (state[currentSlideNo.value] != null)
      drauu.load(state[currentSlideNo.value] || '')
    disableDump = false
    updateState()
  })

  nextTick(() => {
    watch(currentSlideNo, () => {
      if (!drauu.mounted)
        return
      loadCanvas()
    }, { immediate: true })
  })

  drauu.on('start', () => isDrawing.value = true)
  drauu.on('end', () => isDrawing.value = false)

  window.addEventListener('keydown', (e) => {
    if (!drawingEnabled.value || isInputting.value)
      return

    const noModifier = !e.ctrlKey && !e.altKey && !e.shiftKey && !e.metaKey
    let handled = true
    if (e.code === 'KeyZ' && (e.ctrlKey || e.metaKey)) {
      if (e.shiftKey)
        drauu.redo()
      else
        drauu.undo()
    }
    else if (e.code === 'Escape') {
      drawingEnabled.value = false
    }
    else if (e.code === 'KeyL' && noModifier) {
      drawingMode.value = 'line'
    }
    else if (e.code === 'KeyA' && noModifier) {
      drawingMode.value = 'arrow'
    }
    else if (e.code === 'KeyS' && noModifier) {
      drawingMode.value = 'stylus'
    }
    else if (e.code === 'KeyR' && noModifier) {
      drawingMode.value = 'rectangle'
    }
    else if (e.code === 'KeyE' && noModifier) {
      drawingMode.value = 'ellipse'
    }
    else if (e.code === 'KeyC' && noModifier) {
      clearDrauu()
    }
    else if (e.code.startsWith('Digit') && noModifier && +e.code[5] <= brushColors.length) {
      brush.color = brushColors[+e.code[5] - 1]
    }
    else {
      handled = false
    }

    if (handled) {
      e.preventDefault()
      e.stopPropagation()
    }
  }, false)

  if (typeof document !== 'undefined') {
    const style = document.createElement('style')
    style.textContent = `
      .slidev-drawing-enabled {
        cursor: crosshair !important;
      }
      [data-drauu-canvas] {
        pointer-events: auto !important;
        z-index: 100;
        touch-action: none;
      }
      body.slidev-drawing {
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
      }
      body.slidev-drawing .slidev-layout * {
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
      }
      body.slidev-drawing pre,
      body.slidev-drawing code,
      body.slidev-drawing [class*="language-"] {
        user-select: none !important;
        -webkit-user-select: none !important;
        pointer-events: none !important;
      }
      /* 将事件限制缩小到画布区域 */
      body.slidev-drawing .slidev-layout {
        touch-action: none;
        pointer-events: none;
      }
      /* 保持画布可交互 */
      body.slidev-drawing [data-drauu-canvas] {
        pointer-events: auto !important;
        touch-action: none;
      }
      /* 保持工具栏和滑块完全可用 */
      .slidev-toolbar,
      .slidev-toolbar *,
      .slidev-icon-btn,
      [role="slider"],
      input[type="range"],
      .slidev-brush-size,
      .v-popper__wrapper {
        pointer-events: auto !important;
        touch-action: pan-x pan-y !important;
        user-select: auto !important;
        -webkit-user-select: auto !important;
      }
    `
    document.head.appendChild(style)

    // 在启用绘图时立即清除所有选择
    watch(drawingEnabled, (enabled) => {
      document.body.classList.toggle('slidev-drawing', enabled)
      if (enabled) {
        const selection = window.getSelection()
        if (selection) selection.removeAllRanges()
      }
    })

    // 阻止所有可能的选择事件
    document.addEventListener('selectstart', (e) => {
      if (drawingEnabled.value && !(e.target as HTMLElement)?.closest('[data-drauu-canvas]')) {
        if (!isToolbarElement(e.target as HTMLElement)) {
          e.preventDefault()
        }
      }
    }, { capture: true })

    document.addEventListener('mousedown', (e) => {
      if (drawingEnabled.value && !(e.target as HTMLElement)?.closest('[data-drauu-canvas]')) {
        if (!isToolbarElement(e.target as HTMLElement)) {
          e.preventDefault()
          const selection = window.getSelection()
          if (selection) selection.removeAllRanges()
        }
      }
    }, { capture: true })

    // 阻止 iPad 全屏下的拖动手势
    document.addEventListener('touchmove', (e: TouchEvent) => {
      if (drawingEnabled.value && !(e.target as HTMLElement)?.closest('[data-drauu-canvas]')) {
        if (!isToolbarElement(e.target as HTMLElement)) {
          e.preventDefault()
        }
      }
    }, { passive: false, capture: true })
  }

  return {
    brush,
    brushColors,
    canClear,
    canRedo,
    canUndo,
    clear: clearDrauu,
    drauu,
    drauuOptions,
    drawingEnabled,
    drawingMode,
    drawingPinned,
    drawingState,
    isDrawing,
    loadCanvas,
    // Apple Pencil 相关
    applePencilDoubleTapAction,
    handleApplePencilDoubleTap,
    pressureSensitive,
    isApplePencilSupported,
  }
})
