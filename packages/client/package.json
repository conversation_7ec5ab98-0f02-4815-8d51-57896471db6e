{"name": "@slidev/client", "type": "module", "version": "52.0.0", "description": "Presentation slides for developers", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://sli.dev", "repository": {"type": "git", "url": "https://github.com/slidevjs/slidev"}, "bugs": "https://github.com/slidevjs/slidev/issues", "exports": {".": "./index.ts", "./package.json": "./package.json", "./constants": "./constants.ts", "./context": "./context.ts", "./env": "./env.ts", "./layoutHelper": "./layoutHelper.ts", "./routes": "./routes.ts", "./utils": "./utils.ts", "./*": "./*"}, "main": "./public.ts", "engines": {"node": ">=18.0.0"}, "dependencies": {"@antfu/utils": "catalog:frontend", "@iconify-json/carbon": "catalog:icons", "@iconify-json/ph": "catalog:icons", "@iconify-json/svg-spinners": "catalog:icons", "@shikijs/engine-javascript": "catalog:frontend", "@shikijs/monaco": "catalog:monaco", "@shikijs/vitepress-twoslash": "catalog:prod", "@slidev/parser": "workspace:*", "@slidev/rough-notation": "catalog:frontend", "@slidev/types": "workspace:*", "@typescript/ata": "catalog:monaco", "@unhead/vue": "catalog:frontend", "@unocss/reset": "catalog:frontend", "@vueuse/core": "catalog:frontend", "@vueuse/math": "catalog:frontend", "@vueuse/motion": "catalog:frontend", "drauu": "catalog:frontend", "file-saver": "catalog:frontend", "floating-vue": "catalog:frontend", "fuse.js": "catalog:frontend", "katex": "catalog:frontend", "lz-string": "catalog:frontend", "mermaid": "catalog:frontend", "monaco-editor": "catalog:monaco", "nanotar": "catalog:frontend", "pptxgenjs": "catalog:prod", "prettier": "catalog:frontend", "recordrtc": "catalog:frontend", "shiki": "catalog:frontend", "shiki-magic-move": "catalog:frontend", "typescript": "catalog:dev", "unocss": "catalog:prod", "vue": "catalog:frontend", "vue-router": "catalog:frontend", "yaml": "catalog:prod"}, "devDependencies": {"vite": "catalog:prod"}}