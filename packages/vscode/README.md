<br>
<p align="center">
<a href="https://github.com/slidevjs/slidev" target="_blank">
<img src="https://github.com/slidevjs/slidev/blob/main/assets/logo-for-vscode.png?raw=true" alt="Slidev" width="350"/>
</a>
</p>

<p align="center">
Presentation <b>slide</b>s for <b>dev</b>elopers<br>
<sup><del>who uses VS Code 🤣</del></sup>
</p>

<p align="center">
<a href="https://marketplace.visualstudio.com/items?itemName=antfu.slidev" target="__blank"><img src="https://img.shields.io/visual-studio-marketplace/v/antfu.slidev.svg?color=4EC5D4&amp;label=VS%20Code%20Marketplace&logo=visual-studio-code" alt="Visual Studio Marketplace Version" /></a>
<a href="https://marketplace.visualstudio.com/items?itemName=antfu.slidev" target="__blank"><img src="https://img.shields.io/visual-studio-marketplace/d/antfu.slidev.svg?color=2B90B6" alt="Visual Studio Marketplace Downloads" /></a>
<a href="https://kermanx.github.io/reactive-vscode/" target="__blank"><img src="https://img.shields.io/badge/made_with-reactive--vscode-%23007ACC?style=flat&labelColor=%23229863"  alt="Made with reacrive-vscode" /></a>
</p>

<br>

### Features

- Preview slides in the side panel
- Slides tree view
- Re-ordering slides
- Folding for slide blocks
- Multiple slides project support
- Start dev server in one click

<img width="974" src="https://github.com/slidevjs/slidev/assets/63178754/2c9ba01a-d21f-4b33-b6b6-4e249873f865">

## Usage

Click the `Slidev` icon in the activity bar to open the **Slidev panel**. In the Slidev panel, you can see the projects tree view, slides tree view, and the preview webview.

In the **projects tree view**, you can see all the Slidev projects in your workspace. You can click the item to open the corresponding file, and click the `eye` icon over it to switch the active project. The `add` icon allows you to load a slides project that wasn't scanned automatically.

In the **slides tree view**, you can see all the slides in the active project. You can click the item to move your cursor to the slide in the editor, and drag and drop to reorder the slides.

In the **preview webview**, you can click the `run-all` icon to start the dev server and click the `globe` icon to open the slides in the browser. Toggle `lock` icon to sync/unsync the preview navigation with the editor cursor.

There are also some **commands** you can use. Type `Slidev` in the command palette to see them.

## Sponsors

<p align="center">
  <a href="https://antfu.me/sponsor">
    <img src='https://cdn.jsdelivr.net/gh/antfu/static/sponsors.png'/>
  </a>
</p>

## License

MIT License © 2021-2025 [Anthony Fu](https://github.com/antfu)
