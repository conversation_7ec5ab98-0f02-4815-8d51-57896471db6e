name: Test

on:
  push:
    branches:
      - main
      - master

  pull_request:
    branches:
      - main
      - master

jobs:
  test:
    timeout-minutes: 10
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [lts/*]
        os: [ubuntu-latest, windows-latest, macos-latest]
        # os: [ubuntu-latest, macos-latest]
      fail-fast: false

    steps:
      - name: Set git to use LF
        run: |
          git config --global core.autocrlf false
          git config --global core.eol lf

      - uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Setup
        run: npm i -g @antfu/ni

      - name: Install
        run: nci
        env:
          CYPRESS_INSTALL_BINARY: 0

      - name: Build
        run: nr build

      - name: Test
        run: nr test

  cypress:
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Setup
        run: npm i -g @antfu/ni

      - name: Install
        run: nci

      - name: Build
        run: nr build

      - name: Hack Cypress
        run: cp pnpm-lock.yaml package-lock.json

      - name: Cypress
        uses: cypress-io/github-action@v4
        with:
          install-command: echo
          build: nr build
          start: nr cy:fixture
